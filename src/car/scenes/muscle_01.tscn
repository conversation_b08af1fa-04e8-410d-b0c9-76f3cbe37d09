[gd_scene load_steps=7 format=3 uid="uid://b5mq1pjnpypd2"]

[ext_resource type="Texture2D" uid="uid://dwq186evtohnl" path="res://resource/imgs/cars/muscle.png" id="1_0viit"]
[ext_resource type="Script" uid="uid://dkron2jci6qdp" path="res://src/car/car.gd" id="2_ce6o8"]
[ext_resource type="Texture2D" uid="uid://krcf6ai34qxf" path="res://resource/imgs/tires/tires.png" id="3_ce6o8"]

[sub_resource type="AtlasTexture" id="AtlasTexture_mgy7c"]
atlas = ExtResource("1_0viit")
region = Rect2(0, 0, 256, 128)

[sub_resource type="AtlasTexture" id="AtlasTexture_kqefh"]
atlas = ExtResource("3_ce6o8")
region = Rect2(0, 0, 32, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_u0hmj"]
size = Vector2(223.66667, 20)

[node name="Car" type="Node2D"]
script = ExtResource("2_ce6o8")

[node name="Body" type="Sprite2D" parent="."]
scale = Vector2(2, 2)
texture = SubResource("AtlasTexture_mgy7c")

[node name="FrontWheel" type="Sprite2D" parent="."]
position = Vector2(123.333336, 49.333332)
scale = Vector2(2, 2)
texture = SubResource("AtlasTexture_kqefh")

[node name="BackWheel" type="Sprite2D" parent="."]
position = Vector2(-125.333336, 49.333332)
scale = Vector2(2, 2)
texture = SubResource("AtlasTexture_kqefh")

[node name="Node" type="Node" parent="."]

[node name="地面参考线" type="CollisionShape2D" parent="Node"]
visible = false
position = Vector2(0, 112)
scale = Vector2(3, 3)
shape = SubResource("RectangleShape2D_u0hmj")
