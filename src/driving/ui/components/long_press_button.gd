class_name LongPressButton
extends Button

## 长按按钮组件
## 支持长按确认操作，在长按期间显示环形进度条

signal long_press_completed()
signal long_press_started()
signal long_press_cancelled()

@export var long_press_duration: float = 3.0  # 长按持续时间（秒）
@export var progress_color: Color = Color.RED  # 进度条颜色
@export var progress_width: float = 4.0  # 进度条宽度

var is_pressing: bool = false
var press_timer: float = 0.0
var progress_overlay: Control
var tween: Tween

func _ready() -> void:
	# 创建进度条覆盖层
	_create_progress_overlay()
	
	# 连接按钮事件
	button_down.connect(_on_button_down)
	button_up.connect(_on_button_up)

func _create_progress_overlay() -> void:
	"""创建环形进度条覆盖层"""
	progress_overlay = Control.new()
	progress_overlay.name = "ProgressOverlay"
	progress_overlay.mouse_filter = Control.MOUSE_FILTER_IGNORE
	progress_overlay.visible = false
	add_child(progress_overlay)
	
	# 设置覆盖层大小和位置
	progress_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

func _process(delta: float) -> void:
	if is_pressing:
		press_timer += delta

		# 更新进度条显示
		progress_overlay.queue_redraw()

		# 检查是否完成长按
		if press_timer >= long_press_duration:
			_complete_long_press()

func _on_button_down() -> void:
	"""按钮按下时开始长按计时"""
	is_pressing = true
	press_timer = 0.0
	progress_overlay.visible = true
	long_press_started.emit()
	
	# 添加按钮按下的视觉反馈
	if tween:
		tween.kill()
	tween = create_tween()
	tween.tween_property(self, "scale", Vector2(0.95, 0.95), 0.1)

func _on_button_up() -> void:
	"""按钮释放时取消长按"""
	if is_pressing:
		_cancel_long_press()

func _complete_long_press() -> void:
	"""完成长按操作"""
	is_pressing = false
	progress_overlay.visible = false
	long_press_completed.emit()
	
	# 恢复按钮大小
	if tween:
		tween.kill()
	tween = create_tween()
	tween.tween_property(self, "scale", Vector2.ONE, 0.1)

func _cancel_long_press() -> void:
	"""取消长按操作"""
	is_pressing = false
	press_timer = 0.0
	progress_overlay.visible = false
	long_press_cancelled.emit()
	
	# 恢复按钮大小
	if tween:
		tween.kill()
	tween = create_tween()
	tween.tween_property(self, "scale", Vector2.ONE, 0.1)

func _draw() -> void:
	"""绘制按钮（由父类处理）"""
	pass

func _on_progress_overlay_draw() -> void:
	"""绘制环形进度条"""
	if not is_pressing:
		return
		
	var progress = press_timer / long_press_duration
	var center = size / 2
	var radius = min(size.x, size.y) / 2 - progress_width
	
	# 绘制环形进度条
	var angle_from = -PI / 2  # 从顶部开始
	var angle_to = angle_from + (2 * PI * progress)
	
	progress_overlay.draw_arc(center, radius, angle_from, angle_to, 32, progress_color, progress_width)

func _notification(what: int) -> void:
	match what:
		NOTIFICATION_READY:
			# 连接进度条绘制信号
			if progress_overlay:
				progress_overlay.draw.connect(_on_progress_overlay_draw)
		NOTIFICATION_MOUSE_EXIT:
			# 鼠标离开时取消长按
			if is_pressing:
				_cancel_long_press()

## 设置长按持续时间
func set_long_press_duration(duration: float) -> void:
	long_press_duration = duration

## 设置进度条颜色
func set_progress_color(color: Color) -> void:
	progress_color = color

## 设置进度条宽度
func set_progress_width(width: float) -> void:
	progress_width = width

## 获取当前长按进度（0.0 到 1.0）
func get_press_progress() -> float:
	if not is_pressing:
		return 0.0
	return press_timer / long_press_duration

## 是否正在长按
func is_long_pressing() -> bool:
	return is_pressing
