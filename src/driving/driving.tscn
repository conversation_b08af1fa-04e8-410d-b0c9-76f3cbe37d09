[gd_scene load_steps=13 format=3 uid="uid://dpbs3bhrxvuoj"]

[ext_resource type="Script" uid="uid://bir2qeie8k1u8" path="res://src/driving/driving.gd" id="1_whmbh"]
[ext_resource type="Texture2D" uid="uid://bn3uhmicnl1j7" path="res://resource/imgs/sky/slight_purple.png" id="2_88hoa"]
[ext_resource type="Texture2D" uid="uid://qyhuhstamftl" path="res://resource/imgs/sky/clouds/temp.png" id="3_75vba"]
[ext_resource type="Texture2D" uid="uid://cvo6fakgpl8qt" path="res://resource/imgs/guardrail/highway_guardrail.png" id="3_88hoa"]
[ext_resource type="PackedScene" uid="uid://b5mq1pjnpypd2" path="res://src/car/scenes/muscle_01.tscn" id="5_88hoa"]
[ext_resource type="Script" uid="uid://c1r7ii8ubds3b" path="res://src/car/car_generator.gd" id="6_88hoa"]
[ext_resource type="PackedScene" uid="uid://dudhunpff0830" path="res://src/car/scenes/muscle_02.tscn" id="7_23wur"]
[ext_resource type="PackedScene" uid="uid://bp3lri2kmu8rd" path="res://src/car/scenes/muscle_03.tscn" id="8_1wes8"]
[ext_resource type="PackedScene" uid="uid://d0b5yv4f5oidn" path="res://src/car/scenes/muscle_04.tscn" id="9_aikb0"]
[ext_resource type="PackedScene" uid="uid://bv0qwr5ylpgo3" path="res://src/car/scenes/muscle_05.tscn" id="10_53yex"]
[ext_resource type="PackedScene" uid="uid://dwihyainbq3hr" path="res://src/car/scenes/muscle_06.tscn" id="11_bhodi"]
[ext_resource type="PackedScene" uid="uid://bvx8j2k3l4m5n" path="res://src/driving/ui/focus_ui.tscn" id="12_focus_ui"]

[node name="Driving" type="Node2D"]
script = ExtResource("1_whmbh")
speed_scale = 2.0

[node name="Camera2D" type="Camera2D" parent="."]
scale = Vector2(1.5, 1.5)

[node name="Background" type="Node2D" parent="."]
position = Vector2(580, 0)

[node name="Sky" type="Parallax2D" parent="Background"]
repeat_size = Vector2(600, 0)
repeat_times = 6
follow_viewport = false
ignore_camera_scroll = true

[node name="Sprite2D" type="Sprite2D" parent="Background/Sky"]
scale = Vector2(60, 10)
texture = ExtResource("2_88hoa")

[node name="Cloud" type="Parallax2D" parent="Background"]
scroll_offset = Vector2(1, 2)
repeat_size = Vector2(2000, 0)
autoscroll = Vector2(-10, 0)
repeat_times = 3

[node name="Temp" type="Sprite2D" parent="Background/Cloud"]
position = Vector2(100.99997, -63.999985)
scale = Vector2(5, 5)
texture = ExtResource("3_75vba")

[node name="DistantView" type="Parallax2D" parent="Background"]

[node name="MidRange" type="Parallax2D" parent="Background"]

[node name="CloseUp" type="Parallax2D" parent="Background"]

[node name="Guardrail" type="Parallax2D" parent="Background"]
repeat_size = Vector2(82, 0)
autoscroll = Vector2(-500, 0)
repeat_times = 40

[node name="Sprite2D" type="Sprite2D" parent="Background/Guardrail"]
position = Vector2(0, 146)
texture = ExtResource("3_88hoa")

[node name="StreetLight" type="Node2D" parent="Background"]

[node name="Road" type="Parallax2D" parent="Background"]

[node name="Car" parent="." instance=ExtResource("5_88hoa")]
z_index = 10
position = Vector2(0, 90)

[node name="CarGenerator" type="Node" parent="."]
script = ExtResource("6_88hoa")
car_scenes = Array[PackedScene]([ExtResource("5_88hoa"), ExtResource("7_23wur"), ExtResource("8_1wes8"), ExtResource("9_aikb0"), ExtResource("10_53yex"), ExtResource("11_bhodi")])
max_npc = 3
metadata/_custom_type_script = "uid://c1r7ii8ubds3b"

[node name="FocusUI" parent="." instance=ExtResource("12_focus_ui")]
anchors_preset = -1
offset_left = -640.0
offset_top = -360.0
offset_right = 640.0
offset_bottom = 360.0
metadata/_edit_use_anchors_ = true
